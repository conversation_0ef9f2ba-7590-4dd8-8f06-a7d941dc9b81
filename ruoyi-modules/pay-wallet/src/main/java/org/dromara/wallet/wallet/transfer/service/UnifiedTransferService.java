package org.dromara.wallet.wallet.transfer.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.BlockchainType;
import org.dromara.wallet.domain.bo.MetaTransferRecBo;
import org.dromara.wallet.domain.vo.MetaTransferRecVo;
import org.dromara.wallet.service.IMetaTransferRecService;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.wallet.transfer.dto.*;
import org.dromara.wallet.wallet.transfer.enums.TransferBusinessType;
import org.dromara.wallet.wallet.transfer.event.TransferCompletionEvent;
import org.dromara.wallet.wallet.transfer.strategy.BlockchainTransferStrategy;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一转账服务
 *
 * <p>提供跨链转账的统一入口点，具有以下特性：</p>
 * <ul>
 *   <li>单一转账接口，自动检测链类型和代币类型</li>
 *   <li>统一的手续费钱包支持</li>
 *   <li>统一的异常处理和返回值格式</li>
 *   <li>支持TRON、EVM(BSC/ARB/BASE)、Solana等多链</li>
 *   <li>自动原生/合约代币检测</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnifiedTransferService {

    private final List<BlockchainTransferStrategy> transferStrategies;
    private final IMetaTransferRecService transferRecService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 余额刷新服务
     * 用于同步转账完成后自动刷新相关地址的余额
     */
    private final IWalletCoinRecService walletCoinRecService;

    /**
     * 策略映射缓存，提高查找效率
     */
    private Map<String, BlockchainTransferStrategy> strategyMap;

    /**
     * 初始化策略映射
     * 使用 BlockchainType 枚举进行统一映射
     */
    private void initializeStrategyMap() {
        if (strategyMap == null) {
            strategyMap = new HashMap<>();

            // 为每个策略创建映射
            for (BlockchainTransferStrategy strategy : transferStrategies) {
                String strategyType = strategy.getChainName();
                String strategyClassName = strategy.getClass().getSimpleName();

                // 获取该策略类型支持的所有链名称
                for (String chainName : BlockchainType.getChainNamesByStrategyType(strategyType)) {
                    strategyMap.put(chainName, strategy);
                    log.debug("策略映射: {} -> {}", chainName, strategyClassName);
                }
            }

            log.info("转账策略初始化完成，支持的链: {}", strategyMap.keySet());
        }
    }

    /**
     * 统一转账方法 - 主要入口点
     *
     * <p>根据请求中的syncMode自动选择同步或异步执行：</p>
     * <ul>
     *   <li>syncMode=true: 同步执行，等待转账完成后返回结果</li>
     *   <li>syncMode=false: 异步执行，立即返回订单ID，后台处理</li>
     *   <li>自动检测链类型和代币类型</li>
     *   <li>统一的参数验证和错误处理</li>
     *   <li>统一的返回值格式</li>
     * </ul>
     *
     * todo 需要加锁和检查requestId成功唯一
     *
     * @param request 转账请求对象
     * @return 统一的转账结果
     */
    public UnifiedTransferResult transfer(TransferRequest request) {
        log.info("统一转账开始: chain={}, token={}, amount={}, syncMode={}",
            request.getChainName(), request.getTokenSymbol(), request.getAmount(), request.getSyncMode());

        try {
            // 1. 参数验证
            validateTransferRequest(request);

            // 2. 根据模式选择执行方式
            if (Boolean.TRUE.equals(request.getSyncMode())) {
                return executeSyncTransfer(request);
            } else {
                return executeAsyncTransfer(request);
            }

        } catch (Exception e) {
            log.error("统一转账失败: chain={}, token={}, error={}",
                request.getChainName(), request.getTokenSymbol(), e.getMessage(), e);

            if (Boolean.TRUE.equals(request.getSyncMode())) {
                return UnifiedTransferResult.syncFailure(
                    request.getChainName(), "TRANSFER_FAILED", "转账失败: " + e.getMessage());
            } else {
                return UnifiedTransferResult.asyncFailure(
                    request.getChainName(), "TRANSFER_FAILED", "转账失败: " + e.getMessage());
            }
        }
    }

    /**
     * 执行同步转账
     * 直接调用策略执行转账，等待完成后返回结果
     */
    private UnifiedTransferResult executeSyncTransfer(TransferRequest request) {
        log.debug("执行同步转账: chain={}, token={}", request.getChainName(), request.getTokenSymbol());

        // 1. 初始化策略映射
        initializeStrategyMap();

        // 2. 选择转账策略
        BlockchainTransferStrategy strategy = selectTransferStrategy(request.getChainName());

        // 3. 执行转账
        BlockchainTransferResult result = strategy.execute(request);

        // 4. 记录结果
        logTransferResult(request, result);

        // 5. 转换为统一结果
        if (result.isSuccess()) {
            // 同步转账成功后触发余额刷新
            triggerSyncTransferBalanceRefresh(request, result);

            return UnifiedTransferResult.syncSuccess(result);
        } else {
            return UnifiedTransferResult.syncFailure(
                request.getChainName(), result.getErrorCode(), result.getErrorMessage());
        }
    }

    /**
     * 执行异步转账
     * 创建订单记录，异步执行转账，立即返回订单信息
     */
    private UnifiedTransferResult executeAsyncTransfer(TransferRequest request) {
        log.debug("执行异步转账: chain={}, token={}", request.getChainName(), request.getTokenSymbol());

        // 1. 生成请求ID（如果没有）
        if (request.getRequestId() == null) {
            request.setRequestId(UUID.randomUUID().toString());
        }

        // 2. 创建转账记录（立即返回）
        Long recordId = createTransferRecord(request);

        // 3. 异步执行转账（不等待结果）
        request.setTenantId(TenantHelper.getTenantId());
        SpringUtils.getBean(UnifiedTransferService.class).executeTransferAsync(recordId, request);

        // 4. 创建异步结果
        AsyncTransferResult asyncResult = AsyncTransferResult.success(
            recordId, request.getRequestId(), request.getChainName(),
            determineTransferType(request));

        log.info("异步转账记录创建成功: recordId={}, requestId={}, chain={}",
            recordId, request.getRequestId(), request.getChainName());

        // 5. 转换为统一结果
        return UnifiedTransferResult.asyncSuccess(asyncResult);
    }

    // ==================== 向后兼容方法 ====================

    /**
     * 原有的同步转账方法 - 向后兼容
     *
     * @deprecated 建议使用 transfer(TransferRequest) 方法
     */
    @Deprecated
    public BlockchainTransferResult transferLegacy(TransferRequest request) {
        // 强制设置为同步模式
        request.setSyncMode(true);
        UnifiedTransferResult result = transfer(request);

        if (result.isSuccess()) {
            // 构造BlockchainTransferResult
            return BlockchainTransferResult.builder()
                .success(true)
                .chainName(result.getChainName())
                .txHash(result.getTxHash())
                .fromAddress(result.getFromAddress())
                .toAddress(result.getToAddress())
                .transferAmount(result.getAmount())
                .transferTokenSymbol(result.getTokenSymbol())
                .feeProvided(result.getFeeProvided())
                .feeAmount(result.getFeeAmount())
                .confirmationResult(result.getConfirmationResult())
                .build();
        } else {
            return BlockchainTransferResult.failure(
                result.getChainName(), result.getErrorCode(), result.getErrorMessage());
        }
    }

    /**
     * 原有的异步转账方法 - 向后兼容
     *
     * @deprecated 建议使用 transfer(TransferRequest) 方法，设置 syncMode=false
     */
    @Deprecated
    public AsyncTransferResult transferAsyncLegacy(TransferRequest request) {
        // 强制设置为异步模式
        request.setSyncMode(false);
        UnifiedTransferResult result = transfer(request);

        if (result.isSuccess()) {
            // 构造AsyncTransferResult
            return AsyncTransferResult.success(
                result.getOrderId(),
                result.getRequestId(),
                result.getChainName(),
                result.getTransferType()
            );
        } else {
            return AsyncTransferResult.failure(
                result.getChainName(), result.getErrorCode(), result.getErrorMessage());
        }
    }

    /**
     * 原有的简化异步转账方法 - 向后兼容
     *
     * @deprecated 建议使用 transferAsync 方法
     */
    @Deprecated
    public AsyncTransferResult transferAsyncLegacy(String privateKey, String toAddress,
                                                   String amount, String tokenSymbol, String chainName) {
        TransferRequest request = TransferRequest.builder()
            .privateKey(privateKey)
            .toAddress(toAddress)
            .amount(amount)
            .tokenSymbol(tokenSymbol)
            .chainName(chainName)
            .enableFeeWallet(true) // 默认启用手续费钱包
            .syncMode(false) // 强制异步模式
            .build();

        return transferAsyncLegacy(request);
    }

    // ==================== 完全向后兼容的方法名 ====================

    /**
     * 查询转账状态
     *
     * <p>根据订单ID查询转账的当前状态和处理进度</p>
     *
     * @param orderId 转账订单ID
     * @return 转账状态结果
     */
    public TransferStatusResult queryTransferStatus(Long orderId) {
        log.debug("查询转账状态: orderId={}", orderId);

        try {
            // 查询转账记录
            org.dromara.wallet.domain.vo.MetaTransferRecVo record = transferRecService.queryById(orderId);

            if (record == null) {
                log.warn("转账订单不存在: orderId={}", orderId);
                return TransferStatusResult.notFound(orderId);
            }

            // 转换为BO对象以便使用现有的转换逻辑
            org.dromara.wallet.domain.bo.MetaTransferRecBo recordBo =
                new org.dromara.wallet.domain.bo.MetaTransferRecBo();
            // 复制字段
            recordBo.setId(record.getId());
            recordBo.setRequestId(record.getRequestId());
            recordBo.setChainName(record.getChainName());
            recordBo.setTransferType(record.getTransferType());
            recordBo.setConfirmationStatus(record.getConfirmationStatus());
            recordBo.setTransactionHash(record.getTransactionHash());
            recordBo.setRequiredConfirmations(record.getRequiredConfirmations());
            recordBo.setActualConfirmations(record.getActualConfirmations());
            recordBo.setBlockHeight(record.getBlockHeight());
            recordBo.setBlockHash(record.getBlockHash());
            recordBo.setStartTime(record.getStartTime());
            recordBo.setEndTime(record.getEndTime());
            recordBo.setConfirmationTimeMs(record.getConfirmationTimeMs());
            recordBo.setRetryCount(record.getRetryCount());
            recordBo.setMaxRetries(record.getMaxRetries());
            recordBo.setErrorMessage(record.getErrorMessage());
            recordBo.setRemark(record.getRemark());
            // createTime字段在VO中不存在，使用当前时间
            recordBo.setCreateTime(new Date());

            TransferStatusResult result = TransferStatusResult.fromTransferRecord(recordBo);

            log.debug("转账状态查询成功: orderId={}, status={}", orderId, result.getStatus());
            return result;

        } catch (Exception e) {
            log.error("查询转账状态失败: orderId={}, error={}", orderId, e.getMessage(), e);

            return TransferStatusResult.builder()
                .orderId(orderId)
                .status(-1)
                .statusDescription("查询失败")
                .errorMessage("查询转账状态时发生错误: " + e.getMessage())
                .build();
        }
    }

    /**
     * 根据请求ID查询转账状态
     *
     * @param requestId 请求ID
     * @return 转账状态结果
     */
    public TransferStatusResult queryTransferStatusByRequestId(String requestId) {
        log.debug("根据请求ID查询转账状态: requestId={}", requestId);

        try {
            // 构建查询条件
            org.dromara.wallet.domain.bo.MetaTransferRecBo queryBo =
                new org.dromara.wallet.domain.bo.MetaTransferRecBo();
            queryBo.setRequestId(requestId);

            // 查询转账记录列表
            List<org.dromara.wallet.domain.vo.MetaTransferRecVo> records =
                transferRecService.queryList(queryBo);

            if (records == null || records.isEmpty()) {
                log.warn("转账订单不存在: requestId={}", requestId);
                return TransferStatusResult.builder()
                    .requestId(requestId)
                    .status(-1)
                    .statusDescription("订单不存在")
                    .errorMessage("未找到指定请求ID的转账订单")
                    .build();
            }

            // 如果有多个记录，取最新的一个（按ID排序，ID越大越新）
            org.dromara.wallet.domain.vo.MetaTransferRecVo latestRecord = records.get(0);
            if (records.size() > 1) {
                latestRecord = records.stream()
                    .max(Comparator.comparing(MetaTransferRecVo::getId))
                    .orElse(records.get(0));
                log.debug("找到多个转账记录，使用最新的: requestId={}, count={}, latestId={}",
                    requestId, records.size(), latestRecord.getId());
            }

            // 使用订单ID查询详细状态
            return queryTransferStatus(latestRecord.getId());

        } catch (Exception e) {
            log.error("根据请求ID查询转账状态失败: requestId={}, error={}", requestId, e.getMessage(), e);

            return TransferStatusResult.builder()
                .requestId(requestId)
                .status(-1)
                .statusDescription("查询失败")
                .errorMessage("查询转账状态时发生错误: " + e.getMessage())
                .build();
        }
    }

    /**
     * 批量转账方法 - 统一接口
     *
     * @param requests 转账请求列表
     * @return 统一转账结果列表
     */
    public List<UnifiedTransferResult> batchTransfer(List<TransferRequest> requests) {
        log.info("批量转账开始，共{}笔交易", requests.size());

        return requests.stream()
            .map(this::transfer)
            .collect(Collectors.toList());
    }

    /**
     * 验证转账请求参数
     */
    private void validateTransferRequest(TransferRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("转账请求不能为空");
        }
        if (request.getPrivateKey() == null || request.getPrivateKey().trim().isEmpty()) {
            throw new IllegalArgumentException("私钥不能为空");
        }
        if (request.getToAddress() == null || request.getToAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("接收地址不能为空");
        }
        if (request.getAmount() == null || request.getAmount().trim().isEmpty()) {
            throw new IllegalArgumentException("转账金额不能为空");
        }
        if (request.getTokenSymbol() == null || request.getTokenSymbol().trim().isEmpty()) {
            throw new IllegalArgumentException("代币符号不能为空");
        }
        if (request.getChainName() == null || request.getChainName().trim().isEmpty()) {
            throw new IllegalArgumentException("链名称不能为空");
        }
    }

    /**
     * 选择转账策略
     * 使用 BlockchainType 枚举进行链名称验证和规范化
     */
    private BlockchainTransferStrategy selectTransferStrategy(String chainName) {
        // 使用 BlockchainType 验证和规范化链名称
        if (!BlockchainType.isSupported(chainName)) {
            throw new IllegalArgumentException("不支持的区块链: " + chainName +
                "，支持的链: " + BlockchainType.getAllSupportedChainNames());
        }

        // 根据规范化的链名称查找策略
        BlockchainTransferStrategy strategy = strategyMap.get(chainName.toUpperCase());

        if (strategy == null) {
            throw new IllegalArgumentException("未找到对应的转账策略: " + chainName +
                "，支持的链: " + strategyMap.keySet());
        }

        return strategy;
    }

    /**
     * 记录转账结果
     */
    private void logTransferResult(TransferRequest request, BlockchainTransferResult result) {
        if (result.isSuccess()) {
            // 构建确认信息字符串
            String confirmationInfo = buildConfirmationInfo(result);

            log.info("统一转账成功: chain={}, token={}, txHash={}, feeProvided={}, feeAmount={}, confirmation={}",
                request.getChainName(), request.getTokenSymbol(), result.getTxHash(),
                result.isFeeProvided(), result.getFeeAmount(), confirmationInfo);
        } else {
            log.error("统一转账失败: chain={}, token={}, error={}, errorCode={}",
                request.getChainName(), request.getTokenSymbol(),
                result.getErrorMessage(), result.getErrorCode());
        }
    }

    /**
     * 构建确认信息字符串
     */
    private String buildConfirmationInfo(BlockchainTransferResult result) {
        if (!result.isWaitedForConfirmation()) {
            return "未等待确认";
        }

        if (result.getConfirmationResult() != null) {
            return String.format("已确认(%s, %d个确认, 耗时%dms)",
                result.getConfirmationResult().getStatus(),
                result.getConfirmations() != null ? result.getConfirmations() : 0,
                result.getConfirmationResult().getConfirmationTimeMs() != null ?
                    result.getConfirmationResult().getConfirmationTimeMs() : 0);
        }

        return String.format("已确认(%d个确认)",
            result.getConfirmations() != null ? result.getConfirmations() : 0);
    }

    // ==================== 异步转账辅助方法 ====================

    /**
     * 创建转账记录
     *
     * @param request 转账请求
     * @return 转账记录ID
     */
    private Long createTransferRecord(TransferRequest request) {
        try {
            MetaTransferRecBo recordBo = new MetaTransferRecBo();
            recordBo.setTransactionHash(""); // 转账完成后更新
            recordBo.setChainName(request.getChainName());
            recordBo.setConfirmationStatus(0); // 0-待处理
            recordBo.setRequiredConfirmations(0); // 将在异步处理时设置
            recordBo.setActualConfirmations(0);
            recordBo.setStartTime(new Date());
            recordBo.setRetryCount(0);
            recordBo.setMaxRetries(3); // 默认最大重试3次
            recordBo.setTimeoutSeconds(300); // 默认5分钟超时
            recordBo.setCheckIntervalSeconds(10); // 默认10秒检查间隔
            recordBo.setRequestId(request.getRequestId());
            recordBo.setTransferType(determineTransferType(request));
            recordBo.setEventPriority(0); // 默认优先级
            recordBo.setRemark("异步转账记录已创建");

            // 保存转账记录
            transferRecService.insertByBo(recordBo);

            log.debug("转账记录创建成功: recordId={}, chain={}, type={}",
                recordBo.getId(), request.getChainName(), recordBo.getTransferType());

            return recordBo.getId();

        } catch (Exception e) {
            log.error("创建转账记录失败: chain={}, error={}",
                request.getChainName(), e.getMessage(), e);
            throw new RuntimeException("创建转账记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步执行转账
     *
     * @param recordId 转账记录ID
     * @param request  转账请求
     */
    @Async("transferExecutor")
    public void executeTransferAsync(Long recordId, TransferRequest request) {
        log.info("开始异步执行转账: recordId={}, chain={}, token={}, amount={}",
            recordId, request.getChainName(), request.getTokenSymbol(), request.getAmount());

        try {
            // 1. 初始化策略映射
            initializeStrategyMap();

            // 2. 选择转账策略
            BlockchainTransferStrategy strategy = selectTransferStrategy(request.getChainName());

            // 3. 执行转账（包含签名、广播、验证）
            BlockchainTransferResult result = strategy.execute(request);

            // 4. 更新转账记录
            updateTransferRecord(recordId, result);

            // 5. 发布业务完成事件
            publishTransferCompletionEvent(request, result, recordId);

            log.info("异步转账执行完成: recordId={}, success={}, txHash={}",
                recordId, result.isSuccess(), result.getTxHash());

        } catch (Exception e) {
            log.error("异步转账执行失败: recordId={}, error={}",
                recordId, e.getMessage(), e);

            // 更新转账记录为失败状态
            updateTransferRecordFailed(recordId, e);

            // 发布失败事件
            publishTransferCompletionEvent(request, null, recordId, e);
        }
    }

    /**
     * 更新转账记录
     *
     * @param recordId 转账记录ID
     * @param result   转账结果
     */
    private void updateTransferRecord(Long recordId, BlockchainTransferResult result) {
        try {
            MetaTransferRecBo updateBo = new MetaTransferRecBo();
            updateBo.setId(recordId);
            updateBo.setTransactionHash(result.getTxHash());

            if (result.isSuccess()) {
                updateBo.setConfirmationStatus(1); // 1-确认中
                if (result.getConfirmationResult() != null) {
                    // 安全获取确认数量，避免NPE
                    Integer actualConfirmations = result.getConfirmationResult().getActualConfirmations();
                    if (actualConfirmations != null) {
                        updateBo.setActualConfirmations(actualConfirmations);
                    }
                    if (result.getConfirmationResult().isSuccess()) {
                        updateBo.setConfirmationStatus(2); // 2-确认完成
                        updateBo.setEndTime(new Date());
                    }
                }
                updateBo.setRemark("转账执行成功");
            } else {
                updateBo.setConfirmationStatus(3); // 3-执行失败
                updateBo.setErrorMessage(result.getErrorMessage());
                updateBo.setEndTime(new Date());
                updateBo.setRemark("转账执行失败");
            }

            transferRecService.updateByBo(updateBo);

            log.debug("转账记录更新成功: recordId={}, status={}, txHash={}",
                recordId, updateBo.getConfirmationStatus(), result.getTxHash());

        } catch (Exception e) {
            log.error("更新转账记录失败: recordId={}, error={}",
                recordId, e.getMessage());
        }
    }

    /**
     * 更新转账记录为失败状态
     *
     * @param recordId  转账记录ID
     * @param exception 异常信息
     */
    private void updateTransferRecordFailed(Long recordId, Exception exception) {
        try {
            MetaTransferRecBo updateBo = new MetaTransferRecBo();
            updateBo.setId(recordId);
            updateBo.setConfirmationStatus(3); // 3-执行失败
            updateBo.setErrorMessage(exception.getMessage());
            updateBo.setEndTime(new Date());
            updateBo.setRemark("转账执行异常");

            transferRecService.updateByBo(updateBo);

            log.debug("转账记录失败状态更新成功: recordId={}", recordId);

        } catch (Exception e) {
            log.error("更新转账记录失败状态异常: recordId={}, error={}",
                recordId, e.getMessage());
        }
    }

    /**
     * 发布转账完成业务事件
     *
     * @param request  转账请求
     * @param result   转账结果
     * @param recordId 转账记录ID
     */
    private void publishTransferCompletionEvent(TransferRequest request, BlockchainTransferResult result, Long recordId) {
        try {
            TransferCompletionEvent event;

            if (result != null && result.isSuccess()) {
                // 成功事件
                // 安全获取确认数量，避免NPE
                Integer confirmations = null;
                if (result.getConfirmationResult() != null) {
                    confirmations = result.getConfirmationResult().getActualConfirmations();
                }

                event = TransferCompletionEvent.success(
                    this,
                    request.getRequestId(),
                    result.getTxHash(),
                    request.getChainName(),
                    determineTransferType(request),
                    recordId,
                    confirmations,
                    request.getTokenSymbol(),
                    request.getAmount(),
                    request.getTenantId(),
                    request.getStpValue(),
                    result.getFromAddress(),
                    result.getToAddress()
                );
            } else {
                // 失败事件
                String errorMessage = result != null ? result.getErrorMessage() : "转账执行异常";
                event = TransferCompletionEvent.failure(
                    this,
                    request.getRequestId(),
                    result != null ? result.getTxHash() : "",
                    request.getChainName(),
                    determineTransferType(request),
                    recordId,
                    errorMessage,
                    request.getTokenSymbol(),
                    request.getAmount(),
                    request.getTenantId(),
                    request.getStpValue(),
                    "",
                    request.getToAddress()
                );
            }

            // 发布事件
            eventPublisher.publishEvent(event);

            log.info("转账完成业务事件发布成功: recordId={}, requestId={}, success={}",
                recordId, request.getRequestId(), result != null && result.isSuccess());

        } catch (Exception e) {
            log.error("发布转账完成业务事件失败: recordId={}, requestId={}, error={}",
                recordId, request.getRequestId(), e.getMessage());
        }
    }

    /**
     * 发布转账完成业务事件（异常情况）
     *
     * @param request   转账请求
     * @param result    转账结果（可能为null）
     * @param recordId  转账记录ID
     * @param exception 异常信息
     */
    private void publishTransferCompletionEvent(TransferRequest request, BlockchainTransferResult result, Long recordId, Exception exception) {
        try {
            String errorMessage = exception != null ? exception.getMessage() : "未知异常";

            TransferCompletionEvent event = TransferCompletionEvent.failure(
                this,
                request.getRequestId(),
                result != null ? result.getTxHash() : "",
                request.getChainName(),
                determineTransferType(request),
                recordId,
                errorMessage,
                request.getTokenSymbol(),
                request.getAmount(),
                request.getTenantId(),
                request.getStpValue(),
                "",
                request.getToAddress()
            );

            // 发布事件
            eventPublisher.publishEvent(event);

            log.info("转账失败业务事件发布成功: recordId={}, requestId={}, error={}",
                recordId, request.getRequestId(), errorMessage);

        } catch (Exception e) {
            log.error("发布转账失败业务事件失败: recordId={}, requestId={}, error={}",
                recordId, request.getRequestId(), e.getMessage());
        }
    }

    /**
     * 确定转账业务类型
     *
     * @param request 转账请求
     * @return 转账业务类型字符串
     */
    private String determineTransferType(TransferRequest request) {
        // 优先使用请求中指定的业务类型
        if (request.getBusinessType() != null && !request.getBusinessType().trim().isEmpty()) {
            // 使用枚举进行规范化和验证
            return TransferBusinessType.normalize(request.getBusinessType());
        }

        // 如果没有指定业务类型，默认为普通转账
        return TransferBusinessType.getDefault().getCode();
    }

    /**
     * 批量查询转账状态
     *
     * @param requestIds 请求ID列表
     * @return 转账状态结果映射 (requestId -> TransferStatusResult)
     */
    public Map<String, TransferStatusResult> batchQueryTransferStatusByRequestIds(List<String> requestIds) {
        log.debug("批量查询转账状态: requestIds={}", requestIds);

        Map<String, TransferStatusResult> resultMap = new HashMap<>();

        if (requestIds == null || requestIds.isEmpty()) {
            log.warn("批量查询请求ID列表为空");
            return resultMap;
        }

        try {
            // 使用 IN 查询一次性获取所有记录
            org.dromara.wallet.domain.bo.MetaTransferRecBo queryBo =
                new org.dromara.wallet.domain.bo.MetaTransferRecBo();

            // 通过 params 传递 requestIds 列表进行 IN 查询
            Map<String, Object> params = new HashMap<>();
            params.put("requestIdList", requestIds);
            queryBo.setParams(params);

            // 批量查询转账记录
            List<org.dromara.wallet.domain.vo.MetaTransferRecVo> records =
                transferRecService.queryList(queryBo);

            log.debug("批量查询到 {} 条记录", records.size());

            // 按 requestId 分组处理结果
            Map<String, List<org.dromara.wallet.domain.vo.MetaTransferRecVo>> groupedRecords =
                records.stream()
                    .filter(record -> record.getRequestId() != null)
                    .collect(Collectors.groupingBy(
                        org.dromara.wallet.domain.vo.MetaTransferRecVo::getRequestId));

            // 为每个 requestId 生成状态结果
            for (String requestId : requestIds) {
                List<org.dromara.wallet.domain.vo.MetaTransferRecVo> requestRecords =
                    groupedRecords.get(requestId);

                if (requestRecords == null || requestRecords.isEmpty()) {
                    // 未找到记录
                    TransferStatusResult notFoundResult = TransferStatusResult.builder()
                        .requestId(requestId)
                        .status(-1)
                        .statusDescription("订单不存在")
                        .errorMessage("未找到指定请求ID的转账订单")
                        .build();
                    resultMap.put(requestId, notFoundResult);
                } else {
                    // 找到记录，取最新的一个（按ID排序，ID越大越新）
                    org.dromara.wallet.domain.vo.MetaTransferRecVo latestRecord = requestRecords.stream()
                        .max(Comparator.comparing(MetaTransferRecVo::getId))
                        .orElse(requestRecords.get(0));

                    // 转换为 TransferStatusResult
                    TransferStatusResult statusResult = convertToTransferStatusResult(latestRecord);
                    resultMap.put(requestId, statusResult);
                }
            }

            log.debug("批量查询转账状态完成: 请求{}个, 找到{}个", requestIds.size(), resultMap.size());
            return resultMap;

        } catch (Exception e) {
            log.error("批量查询转账状态失败: requestIds={}, error={}", requestIds, e.getMessage(), e);

            // 为所有请求ID返回错误结果
            for (String requestId : requestIds) {
                TransferStatusResult errorResult = TransferStatusResult.builder()
                    .requestId(requestId)
                    .status(-1)
                    .statusDescription("查询失败")
                    .errorMessage("查询转账状态时发生错误: " + e.getMessage())
                    .build();
                resultMap.put(requestId, errorResult);
            }

            return resultMap;
        }
    }

    /**
     * 转换 MetaTransferRecVo 为 TransferStatusResult
     */
    private TransferStatusResult convertToTransferStatusResult(org.dromara.wallet.domain.vo.MetaTransferRecVo record) {
        // 创建 Bo 对象用于转换
        MetaTransferRecBo recordBo = new MetaTransferRecBo();
        recordBo.setId(record.getId());
        recordBo.setRequestId(record.getRequestId());
        recordBo.setTransactionHash(record.getTransactionHash());
        recordBo.setChainName(record.getChainName());
        recordBo.setTransferType(record.getTransferType());
        recordBo.setConfirmationStatus(record.getConfirmationStatus());
        recordBo.setRequiredConfirmations(record.getRequiredConfirmations());
        recordBo.setActualConfirmations(record.getActualConfirmations());
        recordBo.setBlockHeight(record.getBlockHeight());
        recordBo.setBlockHash(record.getBlockHash());
        recordBo.setStartTime(record.getStartTime());
        recordBo.setEndTime(record.getEndTime());
        recordBo.setConfirmationTimeMs(record.getConfirmationTimeMs());
        recordBo.setRetryCount(record.getRetryCount());
        recordBo.setMaxRetries(record.getMaxRetries());
        recordBo.setErrorMessage(record.getErrorMessage());
        recordBo.setRemark(record.getRemark());
        recordBo.setCreateTime(new Date()); // 使用当前时间

        return TransferStatusResult.fromTransferRecord(recordBo);
    }

    /**
     * 触发同步转账完成后的余额刷新
     *
     * @param request 转账请求
     * @param result  转账结果
     */
    private void triggerSyncTransferBalanceRefresh(TransferRequest request, BlockchainTransferResult result) {
        try {
            log.debug("同步转账成功，触发余额刷新: chain={}, fromAddress={}, txHash={}",
                request.getChainName(), result.getFromAddress(), result.getTxHash());

            // 刷新发送地址余额
            walletCoinRecService.refreshBalanceByChain(request.getChainName(), result.getFromAddress());

            log.debug("同步转账余额刷新完成: chain={}, fromAddress={}",
                request.getChainName(), result.getFromAddress());

        } catch (Exception e) {
            log.error("同步转账余额刷新失败: chain={}, fromAddress={}, txHash={}, error={}",
                request.getChainName(), result.getFromAddress(), result.getTxHash(), e.getMessage(), e);
            // 余额刷新失败不影响转账结果返回
        }
    }
}
