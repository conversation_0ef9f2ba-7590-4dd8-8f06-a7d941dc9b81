package org.dromara.wallet.wallet.task;

import cn.hutool.core.thread.ThreadUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.*;
import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.wallet.domain.MetaSolanaCstaddressinfo;
import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import org.dromara.wallet.mapper.MetaBep20CstaddressinfoMapper;
import org.dromara.wallet.mapper.MetaSolanacstaddressinfoMapper;
import org.dromara.wallet.mapper.MetaTrc20CstaddressinfoMapper;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 钱包余额记录定时任务 - 纯扁平化配置版本
 * 完全基于扁平化配置，不依赖V3配置架构
 * <p>
 * 地址表设计说明：
 * - TRON链使用独立的地址表 (MetaTrc20Cstaddressinfo)
 * - EVM兼容链(BSC/ARB/BASE)共用同一个地址表 (MetaBep20Cstaddressinfo)
 * - Solana链使用独立的地址表 (MetaSolanaCstaddressinfo)
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
@JobExecutor(name = "walletRecJobExecutorFlat")
@SuppressWarnings("unused")
public class WalletRecSchedule {

    private final TronConfigFacade tronFacade;
    private final BscConfigFacade bscFacade;
    private final ArbConfigFacade arbFacade;
    private final BaseConfigFacade baseFacade;
    private final SolanaConfigFacade solanaFacade;
    private final MetaTrc20CstaddressinfoMapper trc20Mapper;
    private final MetaBep20CstaddressinfoMapper evmMapper;
    private final MetaSolanacstaddressinfoMapper solanaMapper;
    private final IWalletCoinRecService walletCoinRecService;

    // 链配置映射
    private final Map<String, ChainConfig> chainConfigs = Map.of(
        "TRON", new ChainConfig("tron", this::processTronAddresses),
        "BSC", new ChainConfig("bsc", this::processBscAddresses),
        "ARB", new ChainConfig("arb", this::processArbAddresses),
        "BASE", new ChainConfig("base", this::processBaseAddresses),
        "Solana", new ChainConfig("solana", this::processSolanaAddresses)
    );

    /**
     * 定时查询并记录用户代币余额
     */
    @SuppressWarnings("unused")
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        log.info("🚀 开始执行钱包余额记录任务（纯扁平化配置版本）...");
        try {
            AtomicInteger totalProcessed = new AtomicInteger();
            chainConfigs.forEach((chainName, config) -> {
                try {
                    log.info("🔗 处理{}链...", chainName);
                    config.processor.run();
                    totalProcessed.incrementAndGet();
                } catch (Exception e) {
                    log.error("❌ {}链处理失败", chainName, e);
                    throw new RuntimeException(chainName + "链处理失败", e);
                }
            });
            String resultMessage = String.format("任务完成，共处理 %d 个链", totalProcessed.get());
            log.info("✅ {}", resultMessage);
            return ExecuteResult.success(resultMessage);
        } catch (Exception e) {
            log.error("❌ 钱包余额记录任务执行失败", e);
            return ExecuteResult.failure("任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 处理TRON地址
     */
    private void processTronAddresses() {
        Date startTime = getStartTime();
        List<MetaTrc20Cstaddressinfo> addressList = trc20Mapper.findAddressesNeedUpdate(startTime, "tron");
        processAddresses("TRON", addressList, addressList.size(), (wallet, stats) ->
            walletCoinRecService.processTronWalletWithFlatConfig(
                tronFacade,
                wallet.getCstAddress(),
                wallet.getTenantId()
            ));
    }

    /**
     * 处理BSC地址
     */
    private void processBscAddresses() {
        processEvmAddresses("BSC", address ->
            walletCoinRecService.processBscWalletWithFlatConfig(bscFacade, address));
    }

    /**
     * 处理ARB地址
     */
    private void processArbAddresses() {
        processEvmAddresses("ARB", address ->
            walletCoinRecService.processArbWalletWithFlatConfig(arbFacade, address));
    }

    /**
     * 处理BASE地址
     */
    private void processBaseAddresses() {
        processEvmAddresses("BASE", address ->
            walletCoinRecService.processBaseWalletWithFlatConfig(baseFacade, address));
    }

    /**
     * 处理Solana地址
     */
    private void processSolanaAddresses() {
        Date startTime = getStartTime();
        List<MetaSolanaCstaddressinfo> addressList = solanaMapper.findAddressesNeedUpdate(startTime, "solana");
        processAddresses("Solana", addressList, addressList.size(), (wallet, stats) ->
            walletCoinRecService.processSolanaWalletWithFlatConfig(
            solanaFacade,
            wallet.getCstAddress()
        ));
    }

    /**
     * 通用EVM地址处理
     */
    private void processEvmAddresses(String chainName, Consumer<String> processor) {
        Date startTime = getStartTime();
        List<MetaBep20Cstaddressinfo> addressList = evmMapper.findAddressesNeedUpdate(startTime, chainName.toLowerCase());
        processAddresses(chainName, addressList, addressList.size(), (wallet, stats) -> processor.accept(wallet.getCstAddress()));
    }

    /**
     * 通用地址处理方法
     */
    private <T> void processAddresses(String chainName, List<T> addressList, int total,
                                      BiConsumer<T, int[]> processor) {
        if (addressList.isEmpty()) {
            log.info("{}链没有需要更新的地址", chainName);
            return;
        }

        log.info("{}链找到 {} 个需要更新的地址", chainName, total);
        int[] stats = {0, 0}; // [success, failure]

        for (T wallet : addressList) {
            try {
                ThreadUtil.sleep(300);
                processor.accept(wallet, stats);
                stats[0]++;
            } catch (Exception e) {
                stats[1]++;
                log.error("❌ {}地址处理失败: {}", chainName, wallet, e);
            }
        }

        log.info("{}链处理完成 - 成功: {}, 失败: {}, 总计: {}", chainName, stats[0], stats[1], total);
    }

    /**
     * 获取24小时前的时间
     */
    private Date getStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -24);
        return calendar.getTime();
    }

    /**
     * 链配置类
     */
    private record ChainConfig(String dbName, Runnable processor) {}

}
