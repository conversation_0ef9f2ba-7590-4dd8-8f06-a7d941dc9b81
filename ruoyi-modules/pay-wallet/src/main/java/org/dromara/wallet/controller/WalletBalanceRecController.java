package org.dromara.wallet.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.*;
import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.wallet.domain.MetaSolanaCstaddressinfo;
import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.domain.dto.WalletCollectBo;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.wallet.transfer.dto.TransferRequest;
import org.dromara.wallet.wallet.transfer.dto.UnifiedTransferResult;
import org.dromara.wallet.wallet.transfer.service.UnifiedTransferService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 多链用户钱包代币余额记录控制器
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/coinrec")
public class WalletBalanceRecController extends BaseController {

    private final IWalletCoinRecService walletCoinRecService;
    private final BscConfigFacade bscConfigFacade;
    private final TronConfigFacade tronConfigFacade;
    private final SolanaConfigFacade solanaConfigFacade;
    private final ArbConfigFacade arbConfigFacade;
    private final BaseConfigFacade baseConfigFacade;
    private final IMetaSolanaCstaddressinfoService metaSolanaCstaddressinfoService;
    private final IMetaBep20CstaddressinfoService metaBep20CstaddressinfoService;
    private final IMetaTrc20CstaddressinfoService metaTrc20CstaddressinfoService;
    private final UnifiedTransferService unifiedTransferService;

    /**
     * 归集
     *
     * @param walletCollectBo 归集请求参数
     */
//    @SaCheckPermission("wallet:transfer:collect")
    @Log(title = "区块链钱包归集", businessType = BusinessType.COLLECT)
    @PostMapping("/collect")
    public R<Void> collect(@RequestBody WalletCollectBo walletCollectBo) {

        // 获取归集目标地址
        MetaMainAddress mainAddress;
        // 从数据库获取私钥
        String fromPrivateKey;

        switch (walletCollectBo.getChainType()) {
            case TRON -> {
                // 获取归集目标地址
                mainAddress = tronConfigFacade.getWalletConfig().getMainAddress();
                // 从数据库获取私钥
                MetaTrc20Cstaddressinfo walletInfo = metaTrc20CstaddressinfoService.queryByAddress(walletCollectBo.getFromAddress());
                fromPrivateKey = walletInfo.getCstTrc20private();
            }
            case BSC, ARB, BASE -> {
                // 获取归集目标地址
                mainAddress = bscConfigFacade.getWalletConfig().getMainAddress();
                // 从数据库获取私钥
                MetaBep20Cstaddressinfo walletInfo = metaBep20CstaddressinfoService.queryByAddress(walletCollectBo.getFromAddress());
                fromPrivateKey = walletInfo.getCstTrc20private();
            }
            case SOLANA -> {
                // 获取归集目标地址
                mainAddress = solanaConfigFacade.getWalletConfig().getMainAddress();
                // 从数据库获取私钥
                MetaSolanaCstaddressinfo walletInfo = metaSolanaCstaddressinfoService.queryByAddress(walletCollectBo.getFromAddress());
                fromPrivateKey = walletInfo.getCstPrivate();
            }
            default -> {
                return R.fail("不支持的区块链类型: " + walletCollectBo.getChainType());
            }
        }

        // 使用新的统一转账架构
        TransferRequest request = TransferRequest.builder()
            .privateKey(fromPrivateKey)
            .toAddress(mainAddress.getAddress())
            .amount(walletCollectBo.getAmount().toString())
            .tokenSymbol(walletCollectBo.getTokenSymbol())
            .chainName(walletCollectBo.getChainType().getCode())
            .enableFeeWallet(true)
            .waitForConfirmation(true)
            .memo("钱包归集操作")
            .build();

        // 设置为同步模式
        request.setSyncMode(true);

        // 执行统一转账
        UnifiedTransferResult result = unifiedTransferService.transfer(request);

        if (result.isSuccess()) {
            return R.ok("归集成功，交易哈希: " + result.getTxHash());
        } else {
            return R.fail("归集失败: " + result.getErrorMessage());
        }

    }

    /**
     * 查询多链用户钱包代币余额记录列表
     */
//    @SaCheckPermission("wallet:coinrec:list")
    @SaIgnore
    @GetMapping("/list")
    public TableDataInfo<WalletCoinRecVo> list(WalletCoinRecBo bo, PageQuery pageQuery) {
        return walletCoinRecService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询每个钱包地址和代币组合的最新记录（分页）
     */
//    @SaCheckPermission("wallet:coinrec:list")
    @SaIgnore
    @GetMapping("/latest/page")
    public TableDataInfo<WalletCoinRecVo> latestPage(WalletCoinRecBo bo, PageQuery pageQuery) {
        return walletCoinRecService.queryLatestRecordsPage(bo, pageQuery);
    }

    /**
     * 导出多链用户钱包代币余额记录列表
     */
//    @SaCheckPermission("wallet:coinrec:export")
    @SaIgnore
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<WalletCoinRecVo> export(WalletCoinRecBo bo) {
        return walletCoinRecService.queryList(bo);
    }

    /**
     * 获取多链用户钱包代币余额记录详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("wallet:coinrec:query")
    @GetMapping("/{id}")
    public R<WalletCoinRecVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(walletCoinRecService.queryById(id));
    }

    /**
     * 新增多链用户钱包代币余额记录
     */
//    @SaCheckPermission("wallet:coinrec:add")
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WalletCoinRecBo bo) {
        return toAjax(walletCoinRecService.insertByBo(bo));
    }

    /**
     * 修改多链用户钱包代币余额记录
     */
//    @SaCheckPermission("wallet:coinrec:edit")
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WalletCoinRecBo bo) {
        return toAjax(walletCoinRecService.updateByBo(bo));
    }

    /**
     * 删除多链用户钱包代币余额记录
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("wallet:coinrec:remove")
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(walletCoinRecService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 扫描指定地址和区块链的余额并新增记录
     *
     * @param address   钱包地址
     * @param chainType 区块链类型
     * @return 扫描结果
     */
    @Log(title = "扫描钱包余额", businessType = BusinessType.INSERT)
    @PostMapping("/scan/{address}/{chainType}")
    public R<String> scanWalletBalance(@PathVariable @NotEmpty(message = "钱包地址不能为空") String address,
                                       @PathVariable @NotNull(message = "区块链类型不能为空") ChainType chainType) {
        try {
            log.info("开始扫描钱包余额: address={}, chainType={}", address, chainType);

            // 根据区块链类型调用对应的余额扫描服务
            switch (chainType) {
                case TRON:
                    walletCoinRecService.processTronWalletWithFlatConfig(tronConfigFacade, address, wallet.getTenantId());
                    break;
                case BSC:
                    walletCoinRecService.processBscWalletWithFlatConfig(bscConfigFacade, address);
                    break;
                case ARB:
                    walletCoinRecService.processArbWalletWithFlatConfig(arbConfigFacade, address);
                    break;
                case BASE:
                    walletCoinRecService.processBaseWalletWithFlatConfig(baseConfigFacade, address);
                    break;
                case SOLANA:
                    walletCoinRecService.processSolanaWalletWithFlatConfig(solanaConfigFacade, address);
                    break;
                default:
                    return R.fail("不支持的区块链类型: " + chainType);
            }

            log.info("钱包余额扫描完成: address={}, chainType={}", address, chainType);
            return R.ok("余额扫描完成，已新增记录");

        } catch (Exception e) {
            log.error("扫描钱包余额失败: address={}, chainType={}, error={}", address, chainType, e.getMessage(), e);
            return R.fail("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 批量扫描多个地址的余额并新增记录
     *
     * @param addresses 钱包地址列表（逗号分隔）
     * @param chainType 区块链类型
     * @return 扫描结果
     */
    @Log(title = "批量扫描钱包余额", businessType = BusinessType.INSERT)
    @PostMapping("/scan/batch")
    public R<String> batchScanWalletBalance(@RequestParam @NotEmpty(message = "钱包地址列表不能为空") String addresses,
                                            @RequestParam @NotNull(message = "区块链类型不能为空") ChainType chainType) {
        try {
            // 解析地址列表
            String[] addressArray = addresses.split(",");
            if (addressArray.length == 0) {
                return R.fail("地址列表不能为空");
            }

            log.info("开始批量扫描钱包余额: addresses={}, chainType={}", Arrays.toString(addressArray), chainType);

            int successCount = 0;
            int failureCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (String address : addressArray) {
                String trimmedAddress = address.trim();
                if (trimmedAddress.isEmpty()) {
                    continue;
                }

                try {
                    // 根据区块链类型调用对应的余额扫描服务
                    switch (chainType) {
                        case TRON:
                            walletCoinRecService.processTronWalletWithFlatConfig(tronConfigFacade, trimmedAddress, wallet.getTenantId());
                            break;
                        case BSC:
                            walletCoinRecService.processBscWalletWithFlatConfig(bscConfigFacade, trimmedAddress);
                            break;
                        case ARB:
                            walletCoinRecService.processArbWalletWithFlatConfig(arbConfigFacade, trimmedAddress);
                            break;
                        case BASE:
                            walletCoinRecService.processBaseWalletWithFlatConfig(baseConfigFacade, trimmedAddress);
                            break;
                        case SOLANA:
                            walletCoinRecService.processSolanaWalletWithFlatConfig(solanaConfigFacade, trimmedAddress);
                            break;
                        default:
                            throw new IllegalArgumentException("不支持的区块链类型: " + chainType);
                    }
                    successCount++;
                    log.debug("地址扫描成功: {}", trimmedAddress);

                } catch (Exception e) {
                    failureCount++;
                    String errorMsg = String.format("地址 %s 扫描失败: %s", trimmedAddress, e.getMessage());
                    log.error(errorMsg, e);
                    if (!errorMessages.isEmpty()) {
                        errorMessages.append("; ");
                    }
                    errorMessages.append(errorMsg);
                }
            }

            String resultMessage = String.format("批量扫描完成，成功: %d, 失败: %d", successCount, failureCount);
            log.info("批量钱包余额扫描完成: {}, chainType={}", resultMessage, chainType);

            if (failureCount > 0) {
                return R.ok(resultMessage + "。失败详情: " + errorMessages.toString());
            } else {
                return R.ok(resultMessage);
            }

        } catch (Exception e) {
            log.error("批量扫描钱包余额失败: addresses={}, chainType={}, error={}", addresses, chainType, e.getMessage(), e);
            return R.fail("批量扫描失败: " + e.getMessage());
        }
    }
}
