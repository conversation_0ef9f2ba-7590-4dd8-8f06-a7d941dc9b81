package org.dromara.wallet.wallet.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.monitor.evm.dto.EvmTransactionModel;
import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.Log;
import org.web3j.utils.Numeric;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.List;
import java.util.Set;

/**
 * EVM交易解析器
 * 负责解析Transfer事件，提取交易信息
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>Transfer事件解析：从Log中提取from、to、amount等信息</li>
 *   <li>代币信息获取：获取代币符号和精度</li>
 *   <li>时间戳计算：从区块信息获取真实时间戳</li>
 *   <li>手续费计算：从交易回执计算实际手续费</li>
 *   <li>多链支持：支持BSC、ARB、BASE等EVM链</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EvmTransactionParser {

    private final EvmHelper evmHelper;

    /**
     * ERC20 Transfer事件的topic签名
     * keccak256("Transfer(address,address,uint256)")
     */
    private static final String TRANSFER_EVENT_TOPIC = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    /**
     * 解析Transfer事件，创建交易模型
     *
     * @param transactionLog  交易Log对象
     * @param evmConfigFacade EVM配置门面
     * @return 解析后的交易模型，解析失败时返回null
     */
    public EvmTransactionModel parseTransaction(Log transactionLog, EvmConfigFacade evmConfigFacade) {
        String txHash = transactionLog.getTransactionHash();
        List<String> topics = transactionLog.getTopics();

        // 验证是否为Transfer事件
        if (topics == null || topics.size() < 3) {
            log.debug("{}链交易{}的topics不符合Transfer事件要求", evmConfigFacade.getChainName(), txHash);
            return null;
        }

        if (!TRANSFER_EVENT_TOPIC.equals(topics.get(0))) {
            log.debug("{}链交易{}不是Transfer事件", evmConfigFacade.getChainName(), txHash);
            return null;
        }

        try {
            // 创建交易模型
            EvmTransactionModel transactionModel = EvmTransactionModel.fromLog(transactionLog);

            // 解析Transfer事件
            parseTransferEvent(transactionModel, evmConfigFacade);

            // 设置链信息
            transactionModel.setChainName(evmConfigFacade.getChainName());
            transactionModel.setChainId(evmConfigFacade.getChainId());

            log.debug("{}链成功解析Transfer事件: from={}, to={}, contract={}, amount={}, txHash={}",
                evmConfigFacade.getChainName(),
                transactionModel.getFromAddress(),
                transactionModel.getToAddress(),
                transactionModel.getContractAddress(),
                transactionModel.getAmount(),
                txHash);

            return transactionModel;

        } catch (Exception e) {
            log.error("{}链解析交易{}失败: {}", evmConfigFacade.getChainName(), txHash, e.getMessage());
            return null;
        }
    }

    /**
     * 解析Transfer事件的详细信息
     */
    private void parseTransferEvent(EvmTransactionModel transactionModel, EvmConfigFacade evmConfigFacade) {
        Log transactionLog = transactionModel.getOriginalLog();
        List<String> topics = transactionLog.getTopics();

        // 解析from地址（去掉前缀0x000...）
        String fromAddress = "0x" + topics.get(1).substring(26);
        transactionModel.setFromAddress(fromAddress.toLowerCase());

        // 解析to地址（去掉前缀0x000...）
        String toAddress = "0x" + topics.get(2).substring(26);
        transactionModel.setToAddress(toAddress.toLowerCase());

        // 解析转账金额
        String data = transactionLog.getData();
        if (data != null && !data.equals("0x")) {
            BigInteger rawAmount = Numeric.toBigInt(data);
            transactionModel.setRawAmount(rawAmount);

            // 获取代币信息并转换精度
            String contractAddress = transactionLog.getAddress();
            String tokenSymbol = getTokenSymbol(contractAddress, evmConfigFacade);
            Integer tokenDecimals = getTokenDecimals(contractAddress, evmConfigFacade);

            transactionModel.setTokenSymbol(tokenSymbol);
            transactionModel.setTokenDecimals(tokenDecimals);

            // 转换金额精度
            BigDecimal amount = new BigDecimal(rawAmount)
                .divide(BigDecimal.TEN.pow(tokenDecimals), tokenDecimals, RoundingMode.DOWN);
            transactionModel.setAmount(amount);
        }

        // 设置时间戳（从区块信息获取真实的区块时间戳）
        setTransactionTimestamp(transactionModel, evmConfigFacade);

        // 设置交易手续费（从交易回执获取真实的手续费）
        setTransactionFee(transactionModel, evmConfigFacade);
    }

    /**
     * 获取代币符号
     */
    private String getTokenSymbol(String contractAddress, EvmConfigFacade evmConfigFacade) {
        try {
            // 通过合约地址反查代币符号
            Set<String> enabledTokens = evmConfigFacade.getEnabledTokenSymbols();
            for (String tokenSymbol : enabledTokens) {
                String configAddress = evmConfigFacade.getContractAddress(tokenSymbol);
                if (contractAddress.equalsIgnoreCase(configAddress)) {
                    return tokenSymbol;
                }
            }
            return "UNKNOWN";
        } catch (Exception e) {
            log.warn("{}链获取合约{}的代币符号失败: {}",
                evmConfigFacade.getChainName(), contractAddress, e.getMessage());
            return "UNKNOWN";
        }
    }

    /**
     * 获取代币精度
     */
    private Integer getTokenDecimals(String contractAddress, EvmConfigFacade evmConfigFacade) {
        try {
            // 通过合约地址反查代币精度
            String tokenSymbol = getTokenSymbol(contractAddress, evmConfigFacade);
            if (!"UNKNOWN".equals(tokenSymbol)) {
                return evmConfigFacade.getTokenDecimals(tokenSymbol);
            }
            return 18; // 默认18位精度
        } catch (Exception e) {
            log.warn("{}链获取合约{}的代币精度失败: {}",
                evmConfigFacade.getChainName(), contractAddress, e.getMessage());
            return 18; // 默认18位精度
        }
    }

    /**
     * 设置交易时间戳
     */
    private void setTransactionTimestamp(EvmTransactionModel transactionModel, EvmConfigFacade evmConfigFacade) {
        try {
            Log transactionLog = transactionModel.getOriginalLog();
            BigInteger blockNumber = transactionLog.getBlockNumber();
            if (blockNumber != null) {
                org.web3j.protocol.core.methods.response.EthBlock.Block block =
                    evmHelper.getBlockByNumber(blockNumber, evmConfigFacade);
                if (block != null && block.getTimestamp() != null) {
                    // EVM 区块时间戳是秒级的
                    transactionModel.setTimestamp(block.getTimestamp().longValue());
                } else {
                    log.warn("{}链无法获取区块{}的时间戳，使用当前时间",
                        evmConfigFacade.getChainName(), blockNumber);
                    transactionModel.setTimestamp(System.currentTimeMillis() / 1000);
                }
            } else {
                log.warn("{}链交易{}缺少区块号，使用当前时间",
                    evmConfigFacade.getChainName(), transactionLog.getTransactionHash());
                transactionModel.setTimestamp(System.currentTimeMillis() / 1000);
            }
        } catch (Exception e) {
            log.warn("{}链获取区块时间戳失败，使用当前时间: {}",
                evmConfigFacade.getChainName(), e.getMessage());
            transactionModel.setTimestamp(System.currentTimeMillis() / 1000);
        }
    }

    /**
     * 设置交易手续费
     */
    private void setTransactionFee(EvmTransactionModel transactionModel, EvmConfigFacade evmConfigFacade) {
        try {
            Log transactionLog = transactionModel.getOriginalLog();
            String transactionHash = transactionLog.getTransactionHash();
            if (transactionHash != null) {
                org.web3j.protocol.core.methods.response.TransactionReceipt receipt =
                    evmHelper.getTransactionReceipt(transactionHash, evmConfigFacade);
                if (receipt != null) {
                    // 计算实际支付的手续费：gasUsed * effectiveGasPrice
                    BigInteger gasUsed = receipt.getGasUsed();
                    BigInteger effectiveGasPrice = parseHexToBigInteger(receipt.getEffectiveGasPrice());

                    if (gasUsed != null && effectiveGasPrice != null) {
                        // 计算手续费（Wei单位）
                        BigInteger feeInWei = gasUsed.multiply(effectiveGasPrice);

                        // 转换为ETH/BNB等原生代币单位（Wei转换为Ether，18位小数）
                        BigDecimal feeInNativeToken = new BigDecimal(feeInWei)
                            .divide(new BigDecimal("1000000000000000000"), 18, RoundingMode.DOWN);

                        transactionModel.setTransactionFee(feeInNativeToken);
                        transactionModel.setGasUsed(gasUsed);
                        transactionModel.setGasPrice(effectiveGasPrice);

                        log.debug("{}链交易{}手续费计算成功: gasUsed={}, effectiveGasPrice={}, fee={}",
                            evmConfigFacade.getChainName(), transactionHash, gasUsed, effectiveGasPrice, feeInNativeToken);
                    } else {
                        log.warn("{}链交易{}回执中缺少gas信息: gasUsed={}, effectiveGasPrice={}",
                            evmConfigFacade.getChainName(), transactionHash, gasUsed, effectiveGasPrice);
                    }
                } else {
                    log.warn("{}链无法获取交易{}的回执信息",
                        evmConfigFacade.getChainName(), transactionHash);
                }
            } else {
                log.warn("{}链交易缺少交易哈希", evmConfigFacade.getChainName());
            }
        } catch (Exception e) {
            log.warn("{}链获取交易手续费失败: {}",
                evmConfigFacade.getChainName(), e.getMessage());
        }
    }

    /**
     * 解析十六进制字符串为 BigInteger
     */
    private BigInteger parseHexToBigInteger(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return null;
        }
        try {
            // 移除0x前缀（如果存在）
            if (hexString.startsWith("0x") || hexString.startsWith("0X")) {
                hexString = hexString.substring(2);
            }
            return new BigInteger(hexString, 16);
        } catch (NumberFormatException e) {
            log.warn("解析十六进制字符串失败: {}", hexString);
            return null;
        }
    }
}
