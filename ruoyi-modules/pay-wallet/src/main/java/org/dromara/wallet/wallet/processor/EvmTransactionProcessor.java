package org.dromara.wallet.wallet.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.facade.ArbConfigFacade;
import org.dromara.wallet.config.facade.BaseConfigFacade;
import org.dromara.wallet.config.facade.BscConfigFacade;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.domain.bo.MetaArbTransactionBo;
import org.dromara.wallet.domain.bo.MetaBaseTransactionBo;
import org.dromara.wallet.domain.bo.MetaBep20TransactionBo;
import org.dromara.wallet.service.IMetaArbTransactionService;
import org.dromara.wallet.service.IMetaBaseTransactionService;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.dromara.wallet.service.IMetaBep20TransactionService;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.monitor.evm.dto.EvmTransactionModel;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Set;

/**
 * EVM交易业务处理器
 * 负责统一的EVM交易业务处理逻辑
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>交易分类：根据监控地址分类交易类型（receive/send/collect/unknown）</li>
 *   <li>业务验证：执行最小金额检查和交易可信度验证</li>
 *   <li>数据保存：保存交易记录到对应的数据库表</li>
 *   <li>多链支持：支持BSC、ARB、BASE等EVM链</li>
 *   <li>错误处理：完善的异常处理和状态管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EvmTransactionProcessor {

    // 业务处理相关服务
    private final IMetaArbTransactionService metaArbTransactionService;
    private final IMetaBaseTransactionService metaBaseTransactionService;
    private final IMetaBep20TransactionService metaBep20TransactionService;
    private final IMetaBep20CstaddressinfoService evmAddressService;
    private final EvmHelper evmHelper;

    /**
     * 处理EVM交易的完整业务流程
     *
     * @param transaction     解析后的交易模型
     * @param evmConfigFacade EVM配置门面
     * @return 处理是否成功
     */
    public boolean processTransaction(EvmTransactionModel transaction, EvmConfigFacade evmConfigFacade) {
        try {
            log.debug("{}链开始处理交易: {}", evmConfigFacade.getChainName(), transaction.getFormattedInfo());

            // 1. 分类交易类型
            classifyTransaction(transaction, evmConfigFacade);

            // 2. 根据交易类型处理
            switch (transaction.getTransactionType()) {
                case "receive":
                    return handleReceiveTransaction(transaction, evmConfigFacade);
                case "send":
                    return handleSendTransaction(transaction, evmConfigFacade);
                case "collect":
                    return handleCollectTransaction(transaction, evmConfigFacade);
                case "unknown":
                    log.debug("{}链交易{}类型为unknown，跳过处理",
                        evmConfigFacade.getChainName(), transaction.getTransactionHash());
                    return true; // unknown类型不算处理失败
                default:
                    log.debug("{}链交易{}类型未知，跳过处理",
                        evmConfigFacade.getChainName(), transaction.getTransactionHash());
                    transaction.setTransactionType("unknown");
                    return true;
            }

        } catch (Exception e) {
            log.error("{}链处理交易失败: {}, 错误信息: {}",
                evmConfigFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());
            return false;
        }
    }

    /**
     * 分类交易类型
     */
    private void classifyTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        String fromAddress = transaction.getFromAddress();
        String toAddress = transaction.getToAddress();

        // 获取监控地址列表
        Set<String> monitoredAddresses = TenantHelper.ignore(evmAddressService::queryAllAddressSet);

        if (monitoredAddresses.contains(toAddress)) {
            // 监控地址接收代币
            transaction.setTransactionType("receive");
        } else if (monitoredAddresses.contains(fromAddress)) {
            // 监控地址发送代币
            transaction.setTransactionType("send");
        } else {
            // 非监控地址交易，可能是内部转账或其他
            transaction.setTransactionType("unknown");
        }

        log.debug("{}链交易{}分类为: {}",
            configFacade.getChainName(),
            transaction.getTransactionHash(),
            transaction.getTransactionType());
    }

    /**
     * 处理接收交易
     */
    private boolean handleReceiveTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        log.info("{}链开始处理接收交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 1. 执行验证逻辑
            if (!checkMinimumDepositAmount(transaction, configFacade)) {
                // 金额不符合要求，设置为验证失败
                transaction.setProcessStatus(4);
                transaction.setErrorMessage("金额低于最小入账金额");
                saveTransactionRecord(transaction, configFacade);
                log.info("{}链接收交易验证失败（最小金额）: {}", configFacade.getChainName(), transaction.getTransactionHash());
                return true; // 验证失败也算处理成功，只是状态不同
            }

            if (!verifyTransactionTrustworthiness(transaction, configFacade)) {
                // 交易不可信，设置为验证失败
                transaction.setProcessStatus(5);
                transaction.setErrorMessage("交易可信度验证失败");
                saveTransactionRecord(transaction, configFacade);
                log.info("{}链接收交易验证失败（可信度）: {}", configFacade.getChainName(), transaction.getTransactionHash());
                return true; // 验证失败也算处理成功，只是状态不同
            }

            // 2. 验证通过，保存交易记录到数据库，设置状态为待处理
            transaction.setProcessStatus(0);
            saveTransactionRecord(transaction, configFacade);

            log.info("{}链接收交易验证通过，记录保存成功: {}", configFacade.getChainName(), transaction.getTransactionHash());
            return true;

        } catch (Exception e) {
            log.error("{}链处理接收交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            return false;
        }
    }

    /**
     * 处理发送交易
     */
    private boolean handleSendTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        log.info("{}链处理发送交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 发送交易直接保存记录，设置状态为处理完成
            transaction.setProcessStatus(1);
            saveTransactionRecord(transaction, configFacade);

            log.info("{}链发送交易记录保存完成: {}", configFacade.getChainName(), transaction.getTransactionHash());
            return true;

        } catch (Exception e) {
            log.error("{}链处理发送交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            return false;
        }
    }

    /**
     * 处理归集交易
     */
    private boolean handleCollectTransaction(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        log.info("{}链处理归集交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 归集交易直接保存记录，设置状态为处理完成
            transaction.setProcessStatus(1);
            saveTransactionRecord(transaction, configFacade);

            log.info("{}链归集交易记录保存完成: {}", configFacade.getChainName(), transaction.getTransactionHash());
            return true;

        } catch (Exception e) {
            log.error("{}链处理归集交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage(), e);

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            return false;
        }
    }

    /**
     * 检查最小入账金额
     */
    private boolean checkMinimumDepositAmount(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        try {
            log.debug("{}链检查最小入账金额: txHash={}, amount={}, token={}",
                configFacade.getChainName(), transaction.getTransactionHash(),
                transaction.getAmount(), transaction.getTokenSymbol());

            // 获取交易金额和代币符号
            BigDecimal transactionAmount = transaction.getAmount();
            String tokenSymbol = transaction.getTokenSymbol();

            if (transactionAmount == null || tokenSymbol == null) {
                log.warn("{}链交易{}缺少必要信息: amount={}, token={}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    transactionAmount, tokenSymbol);
                return false;
            }

            // 获取最小入账金额配置
            BigDecimal minDepositAmount = getMinDepositAmount(tokenSymbol, configFacade);
            if (minDepositAmount == null) {
                log.warn("{}链无法获取代币{}的最小入账金额配置，跳过验证",
                    configFacade.getChainName(), tokenSymbol);
                return true; // 无配置时默认通过
            }

            // 比较交易金额与最小金额
            boolean isValid = transactionAmount.compareTo(minDepositAmount) >= 0;

            if (!isValid) {
                log.info("{}链交易{}金额过小: {} {} < {} {}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    transactionAmount, tokenSymbol, minDepositAmount, tokenSymbol);
            } else {
                log.debug("{}链交易{}金额验证通过: {} {} >= {} {}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    transactionAmount, tokenSymbol, minDepositAmount, tokenSymbol);
            }

            return isValid;

        } catch (Exception e) {
            log.error("{}链检查最小入账金额失败: txHash={}, error={}",
                configFacade.getChainName(), transaction.getTransactionHash(), e.getMessage(), e);
            return true; // 出错时默认通过，避免阻塞正常交易
        }
    }

    /**
     * 验证交易可信度
     */
    private boolean verifyTransactionTrustworthiness(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        try {
            log.debug("{}链验证交易可信度: txHash={}, to={}, amount={}, token={}",
                configFacade.getChainName(), transaction.getTransactionHash(),
                transaction.getToAddress(), transaction.getAmount(), transaction.getTokenSymbol());

            // 获取交易信息
            String toAddress = transaction.getToAddress();
            BigDecimal transactionAmount = transaction.getAmount();
            String tokenSymbol = transaction.getTokenSymbol();

            if (toAddress == null || transactionAmount == null || tokenSymbol == null) {
                log.warn("{}链交易{}缺少必要信息: to={}, amount={}, token={}",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    toAddress, transactionAmount, tokenSymbol);
                return false;
            }

            // 查询接收地址的当前余额
            BigDecimal currentBalance = evmHelper.balanceGetForRead(toAddress, tokenSymbol, configFacade);
            if (currentBalance == null) {
                log.warn("{}链无法查询地址{}的{}余额，可信度验证失败",
                    configFacade.getChainName(), toAddress, tokenSymbol);
                return false;
            }

            // 验证余额是否符合预期：当前余额应该 >= 交易金额
            boolean isTrustworthy = currentBalance.compareTo(transactionAmount) >= 0;

            if (!isTrustworthy) {
                log.warn("{}链交易{}可信度验证失败: 当前余额({} {}) < 交易金额({} {})",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    currentBalance, tokenSymbol, transactionAmount, tokenSymbol);
            } else {
                log.debug("{}链交易{}可信度验证通过: 当前余额({} {}) >= 交易金额({} {})",
                    configFacade.getChainName(), transaction.getTransactionHash(),
                    currentBalance, tokenSymbol, transactionAmount, tokenSymbol);
            }

            return isTrustworthy;

        } catch (Exception e) {
            log.error("{}链验证交易可信度失败: txHash={}, error={}",
                configFacade.getChainName(), transaction.getTransactionHash(), e.getMessage(), e);
            return true; // 出错时返回true，避免因网络问题等临时错误阻塞正常交易
        }
    }

    /**
     * 获取最小入账金额配置
     */
    private BigDecimal getMinDepositAmount(String tokenSymbol, EvmConfigFacade configFacade) {
        try {
            // 使用instanceof判断具体的配置类型，调用相应的方法
            if (configFacade instanceof BscConfigFacade bscFacade) {
                return bscFacade.getMinTransferAmount(tokenSymbol);
            } else if (configFacade instanceof ArbConfigFacade arbFacade) {
                return arbFacade.getMinTransferAmount(tokenSymbol);
            } else if (configFacade instanceof BaseConfigFacade baseFacade) {
                return baseFacade.getMinTransferAmount(tokenSymbol);
            } else {
                log.warn("不支持的EVM配置类型: {}", configFacade.getClass().getSimpleName());
                return null;
            }
        } catch (Exception e) {
            log.error("获取{}链代币{}的最小入账金额失败: {}",
                configFacade.getChainName(), tokenSymbol, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存交易记录到数据库
     */
    private void saveTransactionRecord(EvmTransactionModel transaction, EvmConfigFacade configFacade) {
        String chainName = configFacade.getChainName();
        String txHash = transaction.getTransactionHash();

        try {
            log.debug("开始保存{}链交易记录: {}", chainName, txHash);

            // 根据链名称简单判断使用哪个Service
            if ("BSC".equalsIgnoreCase(chainName)) {
                saveBscTransaction(transaction);
            } else if ("ARB".equalsIgnoreCase(chainName)) {
                saveArbTransaction(transaction);
            } else if ("BASE".equalsIgnoreCase(chainName)) {
                saveBaseTransaction(transaction);
            } else {
                log.error("不支持的链类型: {}", chainName);
                throw new RuntimeException("不支持的链类型: " + chainName);
            }

            log.info("{}链交易记录保存成功: txid={}, amount={}, type={}",
                chainName, txHash, transaction.getAmount(), transaction.getTransactionType());

        } catch (Exception e) {
            log.error("保存{}链交易记录失败: {}, 错误信息: {}", chainName, txHash, e.getMessage());
            throw new RuntimeException("保存交易记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存BSC链交易记录
     */
    private void saveBscTransaction(EvmTransactionModel transaction) {
        try {
            // 转换为BSC业务对象
            MetaBep20TransactionBo bo = convertToBscBo(transaction);

            // 保存到数据库
            Boolean result = metaBep20TransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("BSC交易保存失败");
            }
        } catch (Exception e) {
            log.error("保存BSC交易记录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 保存ARB链交易记录
     */
    private void saveArbTransaction(EvmTransactionModel transaction) {
        try {
            // 转换为ARB业务对象
            MetaArbTransactionBo bo = convertToArbBo(transaction);

            // 保存到数据库
            Boolean result = metaArbTransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("ARB交易保存失败");
            }
        } catch (Exception e) {
            log.error("保存ARB交易记录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 保存BASE链交易记录
     */
    private void saveBaseTransaction(EvmTransactionModel transaction) {
        try {
            // 转换为BASE业务对象
            MetaBaseTransactionBo bo = convertToBaseBo(transaction);

            // 保存到数据库
            Boolean result = metaBaseTransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("BASE交易保存失败");
            }
        } catch (Exception e) {
            log.error("保存BASE交易记录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 转换为BSC业务对象
     */
    private MetaBep20TransactionBo convertToBscBo(EvmTransactionModel transaction) {
        MetaBep20TransactionBo bo = new MetaBep20TransactionBo();
        fillCommonFields(bo, transaction);
        return bo;
    }

    /**
     * 转换为ARB业务对象
     */
    private MetaArbTransactionBo convertToArbBo(EvmTransactionModel transaction) {
        MetaArbTransactionBo bo = new MetaArbTransactionBo();
        fillCommonFields(bo, transaction);
        return bo;
    }

    /**
     * 转换为BASE业务对象
     */
    private MetaBaseTransactionBo convertToBaseBo(EvmTransactionModel transaction) {
        MetaBaseTransactionBo bo = new MetaBaseTransactionBo();
        fillCommonFields(bo, transaction);
        return bo;
    }

    /**
     * 填充通用字段
     */
    private void fillCommonFields(Object bo, EvmTransactionModel transaction) {
        try {
            // 使用反射设置字段值
            setField(bo, "txid", transaction.getTransactionHash());
            setField(bo, "blockheight", convertBlockNumber(transaction.getBlockNumber()));
            setField(bo, "address", transaction.getToAddress());
            setField(bo, "fromaddress", transaction.getFromAddress());
            setField(bo, "contract", transaction.getContractAddress());
            setField(bo, "amount", transaction.getAmount());
            setField(bo, "fee", transaction.getTransactionFee());
            setField(bo, "timestamp", transaction.getTimestamp());
            setField(bo, "type", transaction.getTransactionType());
            setField(bo, "issync", transaction.getProcessStatus());
            setField(bo, "remark", buildRemark(transaction));
        } catch (Exception e) {
            log.error("填充交易字段失败: {}", e.getMessage());
            throw new RuntimeException("填充交易字段失败", e);
        }
    }

    /**
     * 使用反射设置字段值
     */
    private void setField(Object obj, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("设置字段{}失败: {}", fieldName, e.getMessage());
            // 某些字段可能不存在，这是正常的，不抛出异常
        }
    }

    /**
     * 转换区块号
     */
    private Long convertBlockNumber(BigInteger blockNumber) {
        if (blockNumber == null) {
            return null;
        }
        try {
            return blockNumber.longValue();
        } catch (Exception e) {
            log.warn("转换区块号失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 构建备注信息
     */
    private String buildRemark(EvmTransactionModel transaction) {
        StringBuilder remark = new StringBuilder();

        // 添加链信息
        if (transaction.getChainName() != null) {
            remark.append("链: ").append(transaction.getChainName());
        }

        // 添加代币信息
        if (transaction.getTokenSymbol() != null) {
            if (!remark.isEmpty()) {
                remark.append(", ");
            }
            remark.append("代币: ").append(transaction.getTokenSymbol());
        }

        // 添加错误信息
        if (transaction.getErrorMessage() != null && !transaction.getErrorMessage().trim().isEmpty()) {
            if (!remark.isEmpty()) {
                remark.append(", ");
            }
            remark.append("错误: ").append(transaction.getErrorMessage());
        }

        return !remark.isEmpty() ? remark.toString() : null;
    }
}
